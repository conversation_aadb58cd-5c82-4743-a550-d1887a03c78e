.example-component {
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    background-color: #f8fafc;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.example-header {
    margin-bottom: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
    padding-bottom: 1rem;
}

.example-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 0.5rem;
}

.example-subtitle {
    color: #718096;
    font-size: 0.875rem;
}

.example-content {
    margin-bottom: 1.5rem;
}

.example-content p {
    margin-bottom: 0.75rem;
    line-height: 1.6;
}

.example-content code {
    background-color: #edf2f7;
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
    font-family: monospace;
    font-size: 0.875rem;
}

.example-footer {
    text-align: right;
}

.example-button {
    background-color: #4c51bf;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.example-button:hover {
    background-color: #434190;
} 