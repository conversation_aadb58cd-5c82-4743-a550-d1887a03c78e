﻿@page "/Account/Manage/TwoFactorAuthentication"

@using Microsoft.AspNetCore.Http.Features
@using Microsoft.AspNetCore.Identity
@using AiBioChat.Data

@inject UserManager<ApplicationUser> UserManager
@inject SignInManager<ApplicationUser> SignInManager
@inject IdentityUserAccessor UserAccessor
@inject IdentityRedirectManager RedirectManager

<PageTitle>Two-factor authentication (2FA)</PageTitle>

<StatusMessage />
<h3>Two-factor authentication (2FA)</h3>
@if (canTrack)
{
    if (is2faEnabled)
    {
        if (recoveryCodesLeft == 0)
        {
            <div class="alert alert-danger">
                <strong>You have no recovery codes left.</strong>
                <p>You must <a href="Account/Manage/GenerateRecoveryCodes">generate a new set of recovery codes</a> before you can log in with a recovery code.</p>
            </div>
        }
        else if (recoveryCodesLeft == 1)
        {
            <div class="alert alert-danger">
                <strong>You have 1 recovery code left.</strong>
                <p>You can <a href="Account/Manage/GenerateRecoveryCodes">generate a new set of recovery codes</a>.</p>
            </div>
        }
        else if (recoveryCodesLeft <= 3)
        {
            <div class="alert alert-warning">
                <strong>You have @recoveryCodesLeft recovery codes left.</strong>
                <p>You should <a href="Account/Manage/GenerateRecoveryCodes">generate a new set of recovery codes</a>.</p>
            </div>
        }

        if (isMachineRemembered)
        {
            <form style="display: inline-block" @formname="forget-browser" @onsubmit="OnSubmitForgetBrowserAsync" method="post">
                <AntiforgeryToken />
                <button type="submit" class="btn btn-primary">Forget this browser</button>
            </form>
        }

        <a href="Account/Manage/Disable2fa" class="btn btn-primary">Disable 2FA</a>
        <a href="Account/Manage/GenerateRecoveryCodes" class="btn btn-primary">Reset recovery codes</a>
    }

    <h4>Authenticator app</h4>
    @if (!hasAuthenticator)
    {
        <a href="Account/Manage/EnableAuthenticator" class="btn btn-primary">Add authenticator app</a>
    }
    else
    {
        <a href="Account/Manage/EnableAuthenticator" class="btn btn-primary">Set up authenticator app</a>
        <a href="Account/Manage/ResetAuthenticator" class="btn btn-primary">Reset authenticator app</a>
    }
}
else
{
    <div class="alert alert-danger">
        <strong>Privacy and cookie policy have not been accepted.</strong>
        <p>You must accept the policy before you can enable two factor authentication.</p>
    </div>
}

@code {
    private bool canTrack;
    private bool hasAuthenticator;
    private int recoveryCodesLeft;
    private bool is2faEnabled;
    private bool isMachineRemembered;

    [CascadingParameter]
    private HttpContext HttpContext { get; set; } = default!;

    protected override async Task OnInitializedAsync()
    {
        var user = await UserAccessor.GetRequiredUserAsync(HttpContext);
        canTrack = HttpContext.Features.Get<ITrackingConsentFeature>()?.CanTrack ?? true;
        hasAuthenticator = await UserManager.GetAuthenticatorKeyAsync(user) is not null;
        is2faEnabled = await UserManager.GetTwoFactorEnabledAsync(user);
        isMachineRemembered = await SignInManager.IsTwoFactorClientRememberedAsync(user);
        recoveryCodesLeft = await UserManager.CountRecoveryCodesAsync(user);
    }

    private async Task OnSubmitForgetBrowserAsync()
    {
        await SignInManager.ForgetTwoFactorClientAsync();

        RedirectManager.RedirectToCurrentPageWithStatus(
            "The current browser has been forgotten. When you login again from this browser you will be prompted for your 2fa code.",
            HttpContext);
    }
}
