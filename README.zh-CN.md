# 项目技术栈

本文档概述了本项目中使用的关键技术。

## 编程语言和框架
- **语言：** C#
- **框架：** ASP.NET Core 8.0

## 用户界面
- **UI 框架：** Blazor (交互式服务器组件)

## 数据访问
- **ORM：** Entity Framework Core 8.0

## 数据库
- **主数据库：** SQLite (已配置)
- **其他支持的数据库：** SQL Server (已包含包)

## 身份验证
- **框架：** ASP.NET Core Identity

## 项目类型
- Web 应用程序

# 项目结构

- **主项目：** AiBioChat (ASP.NET Core Web 应用程序)
  - `Components/`：包含 Blazor 组件。
    - `Layout/`：用于主布局和导航。
    - `Pages/`：用于可路由的页面组件 (例如：Home, Auth, Counter, Weather)。
    - `Account/`：用于 ASP.NET Core Identity 相关的页面和组件。
  - `Data/`：存放数据相关的类。
    - `ApplicationDbContext.cs`：EF Core 数据库上下文，当前包含 Identity 表。
    - `ApplicationUser.cs`：用于 Identity 的自定义用户类。
    - `Migrations/`：EF Core 数据库迁移文件。
  - `Models/`：包含领域模型类。
    - `BaseModel.cs`：模型的占位符基类 (当前为空)。
  - `Services/`：包含服务类。
    - `BaseService.cs`：服务的占位符基类 (当前为空)。
  - `wwwroot/`：用于静态资源 (CSS, JavaScript, 图片)。
  - `Program.cs`：应用程序入口点和服务配置。
  - `AiBioChat.csproj`：定义依赖项和设置的项目文件。
  - `aibio.db`：SQLite 数据库文件。

# 当前状态

- 项目处于早期开发阶段。
- 已建立 Blazor 应用程序的基本基础架构，并集成了 ASP.NET Core Identity 和 EF Core (SQLite)。
- 包括默认的 Blazor 页面 (Home, Counter, Weather) 和 Identity 帐户管理页面。
- 存在占位符类 (`BaseModel.cs`, `BaseService.cs`) 但尚未实现。
- 尚未实现与 "AiBioChat" 相关的特定业务逻辑或功能 (例如：人工智能、生物学、聊天功能)。
- 新帐户的电子邮件确认已禁用。
