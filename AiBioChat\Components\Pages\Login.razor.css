.page-background {
    height: 100%;
    margin: 0;
    font-family: Arial, Helvetica, sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    /* Placeholder background - a simple gradient */
    background-image: linear-gradient(to right top, #051937, #004d7a, #008793, #00bf72, #a8eb12);
}

.login-container {
    background-color: rgba(255, 255, 255, 0.9); /* Semi-transparent white background */
    padding: 40px;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    text-align: center;
    width: 100%;
    max-width: 400px;
}

.logo-placeholder {
    width: 100px;
    height: 100px;
    background-color: #ccc;
    border-radius: 50%;
    margin: 0 auto 20px auto;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.5em;
    color: #fff;
}

.login-form h3 {
    margin-bottom: 25px;
    color: #333;
}

.form-group {
    margin-bottom: 15px;
    text-align: left;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: #555;
}

.form-group input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    box-sizing: border-box; /* Ensures padding doesn't affect overall width */
}

.login-button {
    width: 100%;
    padding: 10px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1em;
}

.login-button:hover {
    background-color: #0056b3;
}

.error-message {
    color: red;
    margin-top: 15px;
}