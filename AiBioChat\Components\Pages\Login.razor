@page "/login"

@attribute [AllowAnonymous]
@layout AiBioChat.Components.Layout.MainLayout

@inject SignInManager<ApplicationUser> SignInManager
@inject NavigationManager NavigationManager

<div class="page-background">
    <div class="login-container">
        <div class="logo-placeholder">Logo</div>
        <div class="login-form">
            <h3>Welcome Back!</h3>
            <EditForm Model="Input" OnValidSubmit="HandleLoginAsync" FormName="loginForm">
                <DataAnnotationsValidator />
                
                <div class="form-group">
                    <label for="email">Email</label>
                    <InputText id="email" class="form-control" @bind-Value="Input.Email" />
                    <ValidationMessage For="() => Input.Email" />
                </div>
                
                <div class="form-group">
                    <label for="password">Password</label>
                    <InputText id="password" type="password" class="form-control" @bind-Value="Input.Password" />
                    <ValidationMessage For="() => Input.Password" />
                </div>
                
                <button type="submit" class="login-button">Log In</button>
            </EditForm>
            @if (!string.IsNullOrEmpty(errorMessage))
            {
                <p class="error-message">@errorMessage</p>
            }
        </div>
    </div>
</div>

@code {
    private LoginInputModel Input { get; set; } = new LoginInputModel();
    private string? errorMessage;
    private bool isSubmitting; // To prevent multiple submissions

    public class LoginInputModel
    {
        [Required(ErrorMessage = "Email is required.")]
        [EmailAddress(ErrorMessage = "Invalid email address.")]
        public string Email { get; set; } = string.Empty;

        [Required(ErrorMessage = "Password is required.")]
        [DataType(DataType.Password)]
        public string Password { get; set; } = string.Empty;
    }

    private async Task HandleLoginAsync()
    {
        if (isSubmitting) return; // Prevent multiple submissions
        isSubmitting = true;
        errorMessage = null; // Clear previous errors

        // Attempt to sign in the user
        // Note: The default SignInManager settings might lock out users on multiple failed attempts.
        // The `lockoutOnFailure` parameter is true by default.
        var result = await SignInManager.PasswordSignInAsync(Input.Email, Input.Password, isPersistent: false, lockoutOnFailure: true);

        if (result.Succeeded)
        {
            NavigationManager.NavigateTo("/", forceLoad: true); // Force load to ensure auth state is fully updated
        }
        else if (result.IsLockedOut)
        {
            errorMessage = "Account locked out. Please try again later.";
        }
        else if (result.IsNotAllowed)
        {
            errorMessage = "Login not allowed. Please ensure your account is confirmed if required.";
        }
        else // Handles general failure like incorrect password
        {
            errorMessage = "Invalid login attempt. Please check your email and password.";
        }
        isSubmitting = false;
        StateHasChanged(); // Re-render the component to show error messages or if navigation didn't occur
    }
}
