@using AiBioChat.Data

<div class="example-component">
    <div class="example-header">
        <h2 class="example-title">示例组件</h2>
        <p class="example-subtitle">这是一个演示CSS隔离的示例组件</p>
    </div>
    
    <div class="example-content">
        <p>这个组件的所有样式都定义在与之对应的 <code>ExampleComponent.razor.css</code> 文件中</p>
        <p>这种方式符合项目的CSS规范要求</p>
    </div>
    
    <div class="example-footer">
        <button class="example-button">示例按钮</button>
    </div>
</div>

@code {
    [Parameter]
    public string Title { get; set; } = "示例组件";
} 