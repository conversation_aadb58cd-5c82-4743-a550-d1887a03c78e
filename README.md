# Project Technology Stack

This document outlines the key technologies used in this project.

## Programming Language and Framework
- **Language:** C#
- **Framework:** ASP.NET Core 8.0

## User Interface
- **UI Framework:** <PERSON><PERSON><PERSON> (Interactive Server Components)

## Data Access
- **ORM:** Entity Framework Core 8.0

## Database
- **Primary Database:** SQLite (configured)
- **Other Supported Databases:** SQL Server (package included)

## Authentication
- **Framework:** ASP.NET Core Identity

## Project Type
- Web Application

# Project Structure

- **Main Project:** AiBioChat (ASP.NET Core Web Application)
  - `Components/`: Contains Blazor components.
    - `Layout/`: For main layout and navigation.
    - `Pages/`: For routable page components (e.g., Home, Auth, Counter, Weather).
    - `Account/`: For ASP.NET Core Identity related pages and components.
  - `Data/`: Holds data-related classes.
    - `ApplicationDbContext.cs`: EF Core database context, currently includes Identity tables.
    - `ApplicationUser.cs`: Custom user class for Identity.
    - `Migrations/`: EF Core database migration files.
  - `Models/`: Contains domain model classes.
    - `BaseModel.cs`: A placeholder base class for models (currently empty).
  - `Services/`: Contains service classes.
    - `BaseService.cs`: A placeholder base class for services (currently empty).
  - `wwwroot/`: For static assets (CSS, JavaScript, images).
  - `Program.cs`: Application entry point and service configuration.
  - `AiBioChat.csproj`: Project file defining dependencies and settings.
  - `aibio.db`: SQLite database file.

# Current Status

- The project is in an early stage of development.
- Basic infrastructure for a Blazor application with ASP.NET Core Identity and EF Core (SQLite) is set up.
- Includes default Blazor pages (Home, Counter, Weather) and Identity account management pages.
- Placeholder classes (`BaseModel.cs`, `BaseService.cs`) exist but are not yet implemented.
- No specific business logic or features related to "AiBioChat" (e.g., AI, biology, chat functionalities) have been implemented yet.
- Email confirmation for new accounts is disabled.
