# AiBioChat 项目 CSS 样式规范

为了提高代码的可维护性和组织性，本项目采用了以下CSS编写规范：

## 组件 CSS 隔离规则

1. **每个组件的 CSS 必须单独放置在一个文件中**
   - 对于组件 `ComponentName.razor`，其样式应该放在 `ComponentName.razor.css` 文件中
   - 严禁在 Razor 文件中使用内联 `<style>` 标签

2. **组件 CSS 隔离的好处**
   - 样式作用域限定在组件内，避免全局污染
   - 提高维护性，使样式和组件明确关联
   - 方便团队协作和代码审查
   - 自动优化打包时的CSS加载

3. **CSS 命名规范**
   - 类名应该遵循组件相关的命名模式，例如 `.login-title`, `.login-subtitle`
   - 避免过于通用的类名，除非确实是全局样式
   - 使用连字符（kebab-case）命名法，如 `.form-container`

4. **全局 CSS**
   - 全局样式应该只放在 `wwwroot/app.css` 中
   - 只有真正需要全局应用的样式才应该放在全局 CSS 文件中
   - 尽量减少全局样式的使用

5. **CSS 优先级**
   - 组件 CSS 将自动生成唯一的选择器来限定作用域
   - 可以使用 `::deep` 选择器修改子组件的样式
   - 了解自动生成的作用域选择器规则

## 实际应用示例

**正确示例**:
```
// ComponentName.razor
<div class="my-component">
    <h1 class="my-component-title">标题</h1>
</div>

// ComponentName.razor.css
.my-component {
    padding: 1rem;
}

.my-component-title {
    color: #333;
}
```

**错误示例**:
```
// ComponentName.razor
<div class="my-component">
    <h1 class="my-component-title">标题</h1>
</div>

<style>
    .my-component {
        padding: 1rem;
    }
    
    .my-component-title {
        color: #333;
    }
</style>
```

## CSS 文件的位置和组织

组件的 CSS 文件应该与组件的 Razor 文件放在同一目录下，并且文件名相同，只是扩展名不同。遵循此规范可以使代码更加整洁和可维护。 